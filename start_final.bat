@echo off
echo ========================================
echo       IndexTTS 1.5 Final Startup
echo ========================================
echo.

echo Activating conda environment...
call conda activate index-tts

echo.
echo Setting environment variables...
set GRADIO_ANALYTICS_ENABLED=False
set GRADIO_SERVER_NAME=127.0.0.1
set GRADIO_SERVER_PORT=7860

echo.
echo Starting IndexTTS WebUI...
echo Visit: http://127.0.0.1:7860
echo.

C:\ProgramData\Anaconda3\envs\index-tts\python.exe -c "import os; os.environ['GRADIO_ANALYTICS_ENABLED']='False'; exec(open('webui.py').read())" --model_dir models/IndexTTS-1.5 --port 7860 --host 127.0.0.1

pause
