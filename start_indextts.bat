@echo off
echo ========================================
echo       IndexTTS 1.5 启动脚本
echo ========================================
echo.

echo [1/3] 激活 conda 环境...
call conda activate index-tts
if errorlevel 1 (
    echo 错误: 无法激活 index-tts 环境
    echo 请确保已正确安装 conda 环境
    pause
    exit /b 1
)

echo [2/3] 检查模型文件...
if not exist "models\IndexTTS-1.5\gpt.pth" (
    echo 错误: 找不到模型文件 models\IndexTTS-1.5\gpt.pth
    echo 请确保模型文件已正确下载到 models\IndexTTS-1.5\ 目录
    pause
    exit /b 1
)

echo [3/3] 启动 IndexTTS WebUI...
echo.
echo ========================================
echo WebUI 启动中，请稍候...
echo 启动完成后，请在浏览器中访问:
echo http://127.0.0.1:7861
echo ========================================
echo.

C:\ProgramData\Anaconda3\envs\index-tts\python.exe webui.py --model_dir models/IndexTTS-1.5 --port 7861

echo.
echo WebUI 已关闭
pause
