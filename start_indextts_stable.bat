@echo off
echo ========================================
echo       IndexTTS 1.5 Startup Script
echo ========================================
echo.

echo [1/3] Activating conda environment...
call conda activate index-tts
if errorlevel 1 (
    echo Error: Cannot activate index-tts environment
    echo Please ensure conda environment is properly installed
    pause
    exit /b 1
)

echo [2/3] Checking model files...
if not exist "models\IndexTTS-1.5\gpt.pth" (
    echo Error: Cannot find model file models\IndexTTS-1.5\gpt.pth
    echo Please ensure model files are downloaded to models\IndexTTS-1.5\ directory
    pause
    exit /b 1
)

echo [3/3] Starting IndexTTS WebUI...
echo.
echo ========================================
echo WebUI is starting, please wait...
echo Trying different ports to avoid conflicts...
echo ========================================
echo.

echo Trying with network fix parameters...
set GRADIO_SERVER_NAME=0.0.0.0
set GRADIO_SERVER_PORT=7863
C:\ProgramData\Anaconda3\envs\index-tts\python.exe webui.py --model_dir models/IndexTTS-1.5 --port 7863 --host 0.0.0.0 --share

if errorlevel 1 (
    echo.
    echo First attempt failed, trying alternative method...
    C:\ProgramData\Anaconda3\envs\index-tts\python.exe -c "import os; os.environ['GRADIO_ANALYTICS_ENABLED'] = 'False'; exec(open('webui.py').read())" --model_dir models/IndexTTS-1.5 --port 8080
)

if errorlevel 1 (
    echo.
    echo Alternative method failed, trying basic launch...
    C:\ProgramData\Anaconda3\envs\index-tts\python.exe webui.py --model_dir models/IndexTTS-1.5 --port 8888 --debug
)

echo.
echo WebUI closed
pause
